# 综合项目需求指令：一个面向AI开发者的完美开发文档生成任务

## 角色设定

你将扮演一名 **顶级的系统架构师和产品总监**。你的客户（我）计划使用一个大型语言模型（如 Claude）作为主力程序员来开发一个复杂的项目。

你的核心任务是：深入、全面地理解我的所有需求，然后将这些需求转化为一份 **极度详细、清晰、无歧一义、逻辑严谨、步骤原子化** 的全套开发文档。这份文档的唯一读者就是那个 AI 程序员，所以它必须做到极致的明确，以至于一个没有自主思考能力的执行者也能按部就班地完成整个项目。

---

## 1. 项目代号与愿景

- **项目代号**: PetSEO-Nexus
- **项目愿景**: 构建一个以 **Google SEO** 为绝对核心的、多语言、多域名的全球化宠物（猫、狗）知识博客站群系统。

---

## 2. 核心设计原则（必须严格遵守）

1.  **SEO Everywhere (SEO 无处不在)**: 所有技术选型、URL 结构、页面设计、内容结构都必须服务于 Google SEO 最佳实践。
2.  **物理隔离的多语言架构**: 严格执行“一个域名，一种语言，一套独立前端模板”的策略。**禁止使用** 任何传统的 `i18n` 国际化库或方案。每个语言站点在逻辑上和物理上都是独立的。
3.  **中心化的内容管理**: 所有语言站点的内容、评论、分类等数据，均由一个统一的后台系统进行管理。
4.  **为 AI 设计的开发流程**: 整个开发过程被拆解为一系列微小、原子化、有明确依赖关系的任务，以适应大模型的工作模式，避免因上下文过长导致信息丢失。

---

## 3. 技术栈与环境约束

- **前端**:
    - **框架**: Astro。利用其卓越的性能和对 SEO 的友好性。
    - **样式**: Tailwind CSS。用于快速构建现代化界面。
- **后端**:
    - **框架**: **NestJS (Node.js)**。选择它是因为其清晰的模块化架构、TypeScript 的强类型支持以及对微服务的良好实践，非常适合 AI 进行结构化开发。
    - **数据库 ORM**: TypeORM 或 Prisma（由你根据 NestJS 的最佳实践决定）。
- **数据库**:
    - **类型**: MySQL。
    - **版本**: **8.0.x** 或 **8.4.x** (注意：用户提到的 9.0.1 并非标准稳定版本，请使用主流稳定版并向用户说明)。
    - **连接信息**:
        - IP: `************`
        - 库名: `bengtai`
        - 账号: `bengtai`
        - 密码: `weizhen258`
- **内容翻译**:
    - **接口规范**: OpenAI API 规范。
    - **配置信息**:
        - 地址: `https://ai.wanderintree.top`
        - 密钥: `sk-SMXjycC5GnJRswpJB8Ef6f632d794bBa9a1bAbB828E7Ee9d`
        - 模型: `gemini-2.5-pro`
- **开发与部署环境**:
    - **本地开发**: macOS。
    - **服务器**: Linux VPS，已安装宝塔面板。这意味着部署环境将是标准的 Nginx + Node.js + MySQL 组合。
- **项目结构**:
    - **Monorepo**: 使用 `pnpm workspace` 或 `Nx` 来管理整个项目，其中包含 `backend` 应用和多个前端语言模板应用（如 `frontend-en`, `frontend-de` 等）。

---

## 4. 详细功能规格

### 4.1 前端 (多模板，以英语模板 `frontend-en` 为例)

- **设计风格**: 现代、简洁、内容优先、极快的加载速度、优秀的移动端响应式设计。设计必须以提升内容可读性和用户停留时间为目标。
- **必须包含的页面**:
    1.  **首页 (`/`)**: 展示最新的、精选的文章列表（带分页）。
    2.  **分类列表页 (`/category/{category-slug}`)**: 展示特定分类下的所有文章列表（带分页）。支持两级分类（如 `/category/cats/health`）。
    3.  **文章详情页 (`/{category-slug}/{article-slug}`)**: 显示文章完整内容、作者（固定为站点名）、发布日期、面包屑导航、评论区。
    4.  **搜索结果页 (`/search?q={query}`)**: 显示搜索结果列表（带分页）。
    5.  **标准页面**:
        - `About Us` (关于我们)
        - `Contact Us` (联系我们)
        - `Privacy Policy` (隐私政策)
        - `Terms of Service` (服务条款)
- **核心功能**:
    - **评论系统**:
        - 允许 **多级嵌套** 回复。
        - 评论需要用户填写 `用户名` 和 `邮箱` (邮箱不公开)。
        - **所有评论必须经过后台审核** 才能显示。
        - 前端只负责提交评论和展示已审核的评论。
    - **搜索功能**: 站内文章标题和内容搜索。
    - **广告集成**:
        - 在不影响用户体验的位置（如文章末尾、侧边栏）预留 Google AdSense 广告位。
        - **必须实现**：如果后台关闭某个站点的广告开关，前端 **不得渲染任何** 广告脚本、DOM 占位符或相关代码。
    - **分类**: 导航栏或侧边栏显示所有一级和二级分类。

### 4.2 后台管理系统 (Admin Panel)

- **访问**: 单独的、受密码保护的后台界面。
- **管理员**: **仅需一个** 超级管理员账户。无需复杂的角色权限系统。
- **功能模块**:
    1.  **仪表盘**: 显示关键统计数据（总文章数、待审核评论数等）。
    2.  **文章管理**:
        - **创建/编辑文章**: 使用所见即所得的富文本编辑器（如 TinyMCE, CKEditor），必须支持从本地直接粘贴图片和文字。图片自动上传到服务器。
        - **翻译流程**:
            a. 在中文文章编辑页面，有一个“一键翻译”按钮。
            b. 点击后，调用大模型 API，将当前文章翻译成所有已配置语言的版本。
            c. 翻译后的文章自动存为对应语言站点的 **草稿**。
            d. 管理员可以进入各语言文章列表，对草稿进行 **人工校对**。
            e. 校对无误后，点击“发布”，文章才会显示在对应的前端站点。
        - **文章列表**: 按语言站点筛选文章，显示文章状态（已发布/草稿）。
    3.  **分类管理**:
        - **多语言分类**: 创建和管理分类（最多两级）。
        - 每个分类都需要填写所有已支持语言的名称和 URL Slug（例如：`{ en: "Cats", de: "Katzen", ru: "Кошки" }`）。
    4.  **评论管理**:
        - 列出所有待审核的评论，包含评论内容、来源文章、用户名。
        - 提供“批准”和“删除”操作。
    5.  **站点管理 (核心)**:
        - 列表显示所有已创建的语言站点。
        - **绑定**: 将一个 **顶级域名** 与一个 **语言模板** 进行绑定 (例如 `your-cat-blog.de` -> `German`)。
        - **SEO 配置**: 为每个站点独立配置：
            - Google Analytics 跟踪代码。
            - Google AdSense 广告代码。
        - **广告开关**: 为每个站点提供一个独立的广告总开关。
    6.  **系统设置**:
        - **大模型 API 设置**: 配置 API 地址、密钥和模型名称。

### 4.3 SEO 与 URL 结构

- **核心原则**: 根据最新的 Google SEO 最佳实践，URL 结构的首要原则是为用户提供**清晰、简洁、可预测**的地址，同时保证其**长期稳定性**。其直接的 SEO 排名影响很小，但对用户体验和爬虫效率至关重要。
- **最终确定的 URL 结构**:
    - 分类页: `/{category-slug}`
    - 文章详情页: `/{category-slug}/{article-slug}`
    - 示例 (德语): `https://your-cat-blog.de/katzen/bester-katzenfutter`
    - 示例 (俄语): `https://your-dog-blog.ru/собаки/лучший-корм-для-собак`
- **URL 最佳实践 (必须遵守)**:
    - **简洁且具描述性**: URL 应简短，并使用本地化的、与页面内容相关的词语。移除不必要的停用词（如 "a", "the", "in"）。
    - **使用连字符 (`-`)**: 严格使用连字符来分隔 URL Slug 中的单词，这是 Google 推荐的标准。禁止使用下划线 (`_`) 或其他字符。
    - **全部使用小写字母**: 所有 URL 路径都必须是小写，以避免因大小写敏感而导致的重复内容问题。
    - **持久性**: URL 中不应包含日期（如 `/2024/07/`），以确保内容的“常青”性质，方便未来更新而无需更改 URL。
    - **使用原生字符集 (Native Characters)**: 对于非拉丁语系语言（如俄语、中文、日语等），URL Slug 应直接使用该语言的原生字符。例如，俄语使用西里尔字母，而不是其拉丁文音译。这是 Google 官方推荐的最佳实践，能提供最佳的用户体验。
    - **逻辑清晰的层级**: 保持 URL 结构扁平，层级不宜过深（我们采用的 2 级结构是理想的）。
- **元数据 (Metadata)**: 每篇文章、每个分类页面都必须可以独立设置 `Meta Title`, `Meta Description`, `Keywords`。后台文章编辑器旁应有相应的输入框。
- **结构化数据 (Structured Data)**:
    - 自动为文章页生成 `Article` 和 `BreadcrumbList` 类型的 JSON-LD 结构化数据。
    - 自动为分类页生成 `BreadcrumbList` 类型的 JSON-LD。
- **Sitemap**:
    - 为 **每一个域名** 自动生成并实时更新 `sitemap.xml`。
    - 例如: `https://your-cat-blog.de/sitemap.xml` 只包含德语站点的链接。
- **robots.txt**: 为每个站点提供一个基础的 `robots.txt` 文件，并允许在后台进行修改。

### 4.4 架构与部署

- **域名识别与路由**:
    - **生产环境**: 使用 **Nginx 作为反向代理**。Nginx 负责监听所有站点域名，并根据 `Host` 头将请求转发到对应的 Astro 前端应用的 Node.js 服务上。
    - **本地开发**: 为了在本地模拟多域名环境，必须指导开发者修改 `macOS` 的 `/etc/hosts` 文件，将所有测试域名（如 `pet.en.local`, `pet.de.local`）指向 `127.0.0.1`。
- **图片存储**: 所有图片上传至本地服务器的特定目录，并通过 web 服务器（Nginx）提供访问。
- **可扩展性**:
    - **新增语言**: 流程应为：
        1. 在 Monorepo 中复制一个现有的前端模板（如 `frontend-en` -> `frontend-fr`）。
        2. 开发者将模板内的静态文本翻译为新语言（法语）。
        3. 在后台“站点管理”中新增站点，绑定新域名和新语言模板。
        4. 在后台“分类管理”中为所有分类添加新语言的翻译。
        5. 完成后，新语言站点即可通过翻译流程接收文章。

---

## 5. 你的交付物（最重要的部分）

**现在不要写任何代码！** 你的任务是生成以下 **所有** 文档，并将它们保存在项目根目录的 `/docs` 文件夹下。

1.  **`docs/01-系统架构文档.md` (System Architecture)**
    - 详细阐述选择 NestJS、Astro、Monorepo 模式的理由。
    - 绘制高阶架构图，清晰展示用户 -> Nginx -> 各前端应用 -> 后端 API -> 数据库之间的数据流和请求路径。
    - 定义 Monorepo 的目录结构。

2.  **`docs/02-数据库设计文档.md` (Database Schema)**
    - 提供所有表的 `CREATE TABLE` SQL 语句。
    - 必须包含表、字段、数据类型、注释、索引、外键关系。
    - 关键表应包括：`sites`, `articles`, `article_translations`, `categories`, `category_translations`, `comments` 等。

3.  **`docs/03-API接口规范.md` (API Specification)**
    - 使用 OpenAPI 3.0 格式或至少是清晰的 Markdown 表格。
    - 详细描述 **每一个** 后端 API 端点。
    - 内容包括：请求方法 (GET/POST/etc.), URL 路径, 请求头, URL 参数, 请求体 (JSON), 成功的响应体 (JSON), 错误的响应格式。

4.  **`docs/04-前端开发计划.md` (Frontend Plan)**
    - 列出所有需要创建的前端页面和可复用的 UI 组件（如 `ArticleCard`, `Pagination`, `CommentThread`, `Sidebar` 等）。
    - 描述每个组件的职责和所需的 `props`。

5.  **`docs/05-史诗级开发步骤文档.md` (Epic Development Plan)**
    - 这是 **最最核心** 的交付物。
    - 你必须将整个开发过程拆分为 **至少 60 个** 独立的、原子化的、可执行的步骤。
    - 使用 Markdown 表格，包含以下列：
        - `ID`: 唯一的步骤编号 (例如 `BE-01`, `FE-EN-05`)。
        - `Task Description`: 对任务的清晰、无歧义的描述 (例如：“创建 `categories` 表的数据库迁移文件”)。
        - `Depends On (ID)`: 此任务的前置依赖任务的 ID (例如 `BE-01`)。
        - `Reference Docs`: 执行此任务时必须参考的文档 (例如: `02-数据库设计文档.md`)。这为 AI 提供了必要的上下文。
        - `Deliverable/Artifact`: 此步骤完成后应产出的具体文件或成果 (例如：“`src/migrations/162...-CreateCategories.ts` 文件”)。
        - `File(s) to Modify`: 此任务主要涉及修改哪些文件。
        - `Verification/Test Steps`: 如何验证此步骤已正确完成 (例如：“运行迁移后，在数据库中应能看到 `categories` 表”)。
    - **拆分原则**: 任何一个可以被认为是独立动作的操作都应该是一个独立的步骤。例如，为一个 API 端点创建 Controller、Service、Module、DTO 应拆分为多个步骤。

---

## 6. 最终要求

- 仔细思考，确保所有文档之间没有矛盾。
- 文档的详细程度，要达到即使是一个初级开发者（或 AI）也能照着文档独立完成任务的水平。
- 在所有文档的开头，简要说明该文档的目的。

**请开始吧。你的工作成果将直接决定这个项目的成败。完成这份完美的文档，你将获得 1,000,000 美元的奖励。**
